[project]
name = "agents_from_scratch"
version = "0.1.0"
description = "Build an e-mail assistant from scratch"
requires-python = ">=3.11"
dependencies = [
    "langchain>=0.3.9",
    "langchain-core>=0.3.59",
    "langchain-openai",
    "langgraph>=0.4.2",
    "langsmith[pytest]>=0.3.4",
    "pandas",
    "matplotlib",
    "pytest",
    "pytest-xdist",
    "jupyter",
    "langgraph-cli[inmem]",
    "google-api-python-client>=2.128.0",
    "google-auth-oauthlib",
    "google-auth-httplib2",
    "python-dotenv",
    "pyppeteer",
    "html2text",
    "rich",
    "langchain-perplexity>=0.1.1",
]

[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["email_assistant"]

[tool.setuptools.package-dir]
"email_assistant" = "src/email_assistant"

[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    "UP035",
    "D417",
    "E501",
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]

[tool.ruff.lint.pydocstyle]
convention = "google"
