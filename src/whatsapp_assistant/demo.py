"""
Demo script for the Simple Perplexity Assistant

This script demonstrates how to use the simple WhatsApp assistant
that integrates with Perplexity API.

Before running:
1. Set your PPLX_API_KEY environment variable
2. Install dependencies: uv sync --extra dev

Usage:
    python src/whatsapp_assistant/demo.py
"""

import os
import sys
from pathlib import Path

# Add the src directory to Python path so we can import our modules
src_path = Path(__file__).parent.parent
sys.path.insert(0, str(src_path))

from whatsapp_assistant import create_simple_assistant, run_simple_assistant


def main():
    """Main demo function"""
    
    print("🤖 Simple Perplexity Assistant Demo")
    print("=" * 40)
    
    # Check for API key
    if not os.getenv("PPLX_API_KEY"):
        print("❌ Error: PPLX_API_KEY environment variable not found!")
        print("\n📝 To get started:")
        print("1. Sign up at https://www.perplexity.ai/")
        print("2. Get your API key from https://www.perplexity.ai/settings/api")
        print("3. Set the environment variable:")
        print("   export PPLX_API_KEY='your-api-key-here'")
        print("\n💡 Or add it to your .env file:")
        print("   PPLX_API_KEY=your-api-key-here")
        return
    
    print("✅ API key found!")
    print("\n🔧 Creating assistant...")
    
    try:
        # Create the assistant
        assistant = create_simple_assistant()
        print("✅ Assistant created successfully!")
        
        # Demo questions
        demo_questions = [
            "What's the latest news about artificial intelligence?",
            "How does the weather look today?",
            "What are some interesting facts about space exploration?"
        ]
        
        print("\n🎯 Running demo questions...")
        print("-" * 40)
        
        for i, question in enumerate(demo_questions, 1):
            print(f"\n{i}. 🤔 Question: {question}")
            print("   🤖 Assistant: ", end="")
            
            try:
                result = assistant.invoke({
                    "user_input": question,
                    "response": "",
                    "messages": []
                })
                
                # Print response with word wrapping
                response = result["response"]
                words = response.split()
                line_length = 0
                for word in words:
                    if line_length + len(word) + 1 > 70:  # Wrap at 70 chars
                        print(f"\n               {word}", end="")
                        line_length = len(word)
                    else:
                        if line_length > 0:
                            print(f" {word}", end="")
                            line_length += len(word) + 1
                        else:
                            print(word, end="")
                            line_length = len(word)
                print()  # New line after response
                
            except Exception as e:
                print(f"❌ Error: {e}")
        
        # Interactive mode
        print("\n" + "=" * 40)
        print("💬 Interactive Mode")
        print("Type your questions below (or 'quit' to exit)")
        print("-" * 40)
        
        while True:
            try:
                user_input = input("\n🤔 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q', 'bye']:
                    print("👋 Thanks for trying the assistant! Goodbye!")
                    break
                
                if not user_input:
                    print("💭 Please enter a question or message.")
                    continue
                
                print("🤖 Assistant: ", end="")
                response = run_simple_assistant(user_input)
                
                # Print response with word wrapping
                words = response.split()
                line_length = 0
                for word in words:
                    if line_length + len(word) + 1 > 70:
                        print(f"\n              {word}", end="")
                        line_length = len(word)
                    else:
                        if line_length > 0:
                            print(f" {word}", end="")
                            line_length += len(word) + 1
                        else:
                            print(word, end="")
                            line_length = len(word)
                print()  # New line after response
                
            except KeyboardInterrupt:
                print("\n\n👋 Interrupted by user. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    except Exception as e:
        print(f"❌ Failed to create assistant: {e}")
        print("\n🔍 Troubleshooting:")
        print("1. Make sure your PPLX_API_KEY is correct")
        print("2. Check your internet connection")
        print("3. Verify that langchain-perplexity is installed")


if __name__ == "__main__":
    main()
