"""
Simple WhatsApp Assistant using Perplexity API

This module creates a basic LangGraph workflow that:
1. Takes user input
2. Sends it to Perplexity's cheapest model (sonar-online)
3. Returns the response

Usage:
    from whatsapp_assistant.simple_perplexity_assistant import create_simple_assistant
    
    assistant = create_simple_assistant()
    response = assistant.invoke({"user_input": "What's the weather like today?"})
    print(response["response"])
"""

import os
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langchain_perplexity import ChatPerplexity
from langchain_core.messages import HumanMessage, AIMessage


class SimpleAssistantState(TypedDict):
    """State for the simple assistant workflow"""
    user_input: str
    response: str
    messages: list


def create_perplexity_model():
    """Create and configure the Perplexity model (cheapest option)"""
    
    # Check for API key
    api_key = os.getenv("PPLX_API_KEY")
    if not api_key:
        raise ValueError(
            "PPLX_API_KEY environment variable is required. "
            "Get your API key from https://www.perplexity.ai/settings/api"
        )
    
    # Use sonar-online which is the cheapest Perplexity model
    return ChatPerplexity(
        model="sonar-online",  # Cheapest model
        temperature=0.7,
        pplx_api_key=api_key
    )


def perplexity_node(state: SimpleAssistantState) -> dict:
    """
    Node that processes user input with Perplexity
    
    Args:
        state: Current state containing user_input
        
    Returns:
        Updated state with response and messages
    """
    
    user_input = state.get("user_input", "")
    
    if not user_input.strip():
        return {
            "response": "Please provide a question or message.",
            "messages": state.get("messages", [])
        }
    
    try:
        # Initialize the model
        perplexity_llm = create_perplexity_model()
        
        # Create the message
        human_message = HumanMessage(content=user_input)
        
        # Get response from Perplexity
        ai_response = perplexity_llm.invoke([human_message])
        
        # Update messages list
        updated_messages = state.get("messages", []) + [human_message, ai_response]
        
        return {
            "response": ai_response.content,
            "messages": updated_messages
        }
        
    except Exception as e:
        error_message = f"Error calling Perplexity API: {str(e)}"
        return {
            "response": error_message,
            "messages": state.get("messages", [])
        }


def create_simple_assistant():
    """
    Create a simple assistant graph using Perplexity
    
    Returns:
        Compiled LangGraph that can process user inputs
    """
    
    # Create the state graph
    builder = StateGraph(SimpleAssistantState)
    
    # Add the Perplexity processing node
    builder.add_node("perplexity", perplexity_node)
    
    # Define the flow: START -> perplexity -> END
    builder.add_edge(START, "perplexity")
    builder.add_edge("perplexity", END)
    
    # Compile and return the graph
    return builder.compile()


def run_simple_assistant(user_input: str) -> str:
    """
    Convenience function to run the assistant with a single input
    
    Args:
        user_input: The user's question or message
        
    Returns:
        The assistant's response as a string
    """
    
    assistant = create_simple_assistant()
    
    result = assistant.invoke({
        "user_input": user_input,
        "response": "",
        "messages": []
    })
    
    return result["response"]

whatsapp_assistant = create_simple_assistant()

# Example usage and testing
if __name__ == "__main__":
    # Example usage
    print("Simple Perplexity Assistant")
    print("=" * 30)
    
    # Check if API key is set
    if not os.getenv("PPLX_API_KEY"):
        print("❌ Please set PPLX_API_KEY environment variable")
        print("Get your API key from: https://www.perplexity.ai/settings/api")
        exit(1)
    
    # Test the assistant
    test_questions = [
        "What's the current weather in San Francisco?",
        "Tell me about the latest developments in AI",
        "What are the top news stories today?"
    ]
    
    assistant = create_simple_assistant()
    
    for question in test_questions:
        print(f"\n🤔 Question: {question}")
        print("🤖 Assistant: ", end="")
        
        try:
            result = assistant.invoke({
                "user_input": question,
                "response": "",
                "messages": []
            })
            print(result["response"])
            
        except Exception as e:
            print(f"Error: {e}")
        
        print("-" * 50)
    
    # Interactive mode
    print("\n💬 Interactive mode (type 'quit' to exit):")
    while True:
        user_question = input("\nYou: ").strip()
        
        if user_question.lower() in ['quit', 'exit', 'q']:
            print("👋 Goodbye!")
            break
            
        if user_question:
            try:
                response = run_simple_assistant(user_question)
                print(f"🤖 Assistant: {response}")
            except Exception as e:
                print(f"❌ Error: {e}")
